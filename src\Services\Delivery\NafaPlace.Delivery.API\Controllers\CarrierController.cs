using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using NafaPlace.Delivery.Application.Services;
using NafaPlace.Delivery.Application.DTOs;

namespace NafaPlace.Delivery.API.Controllers;

[ApiController]
[Route("api/[controller]")]
public class CarrierController : ControllerBase
{
    private readonly IDeliveryService _deliveryService;
    private readonly ILogger<CarrierController> _logger;

    public CarrierController(IDeliveryService deliveryService, ILogger<CarrierController> logger)
    {
        _deliveryService = deliveryService;
        _logger = logger;
    }

    [HttpGet]
    public async Task<ActionResult<List<CarrierDto>>> GetCarriers([FromQuery] bool activeOnly = true)
    {
        try
        {
            var carriers = await _deliveryService.GetCarriersAsync(activeOnly);
            return Ok(carriers);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting carriers");
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpGet("{id}")]
    public async Task<ActionResult<CarrierDto>> GetCarrier(int id)
    {
        try
        {
            var carrier = await _deliveryService.GetCarrierAsync(id);
            if (carrier == null)
                return NotFound();

            return Ok(carrier);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting carrier {Id}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpGet("code/{code}")]
    public async Task<ActionResult<CarrierDto>> GetCarrierByCode(string code)
    {
        try
        {
            var carrier = await _deliveryService.GetCarrierByCodeAsync(code);
            if (carrier == null)
                return NotFound();

            return Ok(carrier);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting carrier by code {Code}", code);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpPost]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<CarrierDto>> CreateCarrier([FromBody] CreateCarrierRequest request)
    {
        try
        {
            var carrier = await _deliveryService.CreateCarrierAsync(request);
            return CreatedAtAction(nameof(GetCarrier), new { id = carrier.Id }, carrier);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating carrier");
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpPut("{id}")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<CarrierDto>> UpdateCarrier(int id, [FromBody] UpdateCarrierRequest request)
    {
        try
        {
            var carrier = await _deliveryService.UpdateCarrierAsync(id, request);
            return Ok(carrier);
        }
        catch (InvalidOperationException)
        {
            return NotFound();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating carrier {Id}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpDelete("{id}")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult> DeleteCarrier(int id)
    {
        try
        {
            var result = await _deliveryService.DeleteCarrierAsync(id);
            if (!result)
                return NotFound();

            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting carrier {Id}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpGet("{carrierId}/zones")]
    public async Task<ActionResult<List<CarrierZoneDto>>> GetCarrierZones(int carrierId)
    {
        try
        {
            var carrierZones = await _deliveryService.GetCarrierZonesAsync(carrierId);
            return Ok(carrierZones);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting carrier zones for carrier {CarrierId}", carrierId);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpPost("zones")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<CarrierZoneDto>> CreateCarrierZone([FromBody] CreateCarrierZoneRequest request)
    {
        try
        {
            var carrierZone = await _deliveryService.CreateCarrierZoneAsync(request);
            return Ok(carrierZone);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating carrier zone");
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpPut("zones/{id}")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<CarrierZoneDto>> UpdateCarrierZone(int id, [FromBody] UpdateCarrierZoneRequest request)
    {
        try
        {
            var carrierZone = await _deliveryService.UpdateCarrierZoneAsync(id, request);
            return Ok(carrierZone);
        }
        catch (InvalidOperationException)
        {
            return NotFound();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating carrier zone {Id}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpDelete("zones/{id}")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult> DeleteCarrierZone(int id)
    {
        try
        {
            var result = await _deliveryService.DeleteCarrierZoneAsync(id);
            if (!result)
                return NotFound();

            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting carrier zone {Id}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpGet("health")]
    public IActionResult Health()
    {
        return Ok(new { status = "healthy", service = "carrier-api", timestamp = DateTime.UtcNow });
    }
}
