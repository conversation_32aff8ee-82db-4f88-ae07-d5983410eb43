﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using NafaPlace.Delivery.Infrastructure.Data;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace NafaPlace.Delivery.Infrastructure.Migrations
{
    [DbContext(typeof(DeliveryDbContext))]
    partial class DeliveryDbContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.0")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("NafaPlace.Delivery.Domain.Models.Carrier", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Address")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("ApiEndpoint")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("ApiKey")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("ApiSecret")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("ContactEmail")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("ContactPhone")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<string>("LogoUrl")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<decimal>("Rating")
                        .HasColumnType("numeric");

                    b.Property<int>("ReviewCount")
                        .HasColumnType("integer");

                    b.Property<int>("SuccessfulDeliveries")
                        .HasColumnType("integer");

                    b.Property<int>("TotalDeliveries")
                        .HasColumnType("integer");

                    b.Property<int>("Type")
                        .HasColumnType("integer");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("Website")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.HasKey("Id");

                    b.ToTable("Carriers");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            Code = "NPE",
                            ContactEmail = "<EMAIL>",
                            ContactPhone = "+224621000001",
                            CreatedAt = new DateTime(2025, 7, 20, 6, 46, 55, 206, DateTimeKind.Utc).AddTicks(5505),
                            Description = "Service de livraison express NafaPlace",
                            IsActive = true,
                            Name = "NafaPlace Express",
                            Rating = 0m,
                            ReviewCount = 0,
                            SuccessfulDeliveries = 0,
                            TotalDeliveries = 0,
                            Type = 1,
                            UpdatedAt = new DateTime(2025, 7, 20, 6, 46, 55, 206, DateTimeKind.Utc).AddTicks(5505)
                        },
                        new
                        {
                            Id = 2,
                            Code = "GDL",
                            ContactEmail = "<EMAIL>",
                            ContactPhone = "+224621000002",
                            CreatedAt = new DateTime(2025, 7, 20, 6, 46, 55, 206, DateTimeKind.Utc).AddTicks(6033),
                            Description = "Service de livraison national",
                            IsActive = true,
                            Name = "Guinée Livraison",
                            Rating = 0m,
                            ReviewCount = 0,
                            SuccessfulDeliveries = 0,
                            TotalDeliveries = 0,
                            Type = 2,
                            UpdatedAt = new DateTime(2025, 7, 20, 6, 46, 55, 206, DateTimeKind.Utc).AddTicks(6034)
                        },
                        new
                        {
                            Id = 3,
                            Code = "RDG",
                            ContactEmail = "<EMAIL>",
                            ContactPhone = "+224621000003",
                            CreatedAt = new DateTime(2025, 7, 20, 6, 46, 55, 206, DateTimeKind.Utc).AddTicks(6035),
                            Description = "Livraison rapide en Guinée",
                            IsActive = true,
                            Name = "Rapid Delivery GN",
                            Rating = 0m,
                            ReviewCount = 0,
                            SuccessfulDeliveries = 0,
                            TotalDeliveries = 0,
                            Type = 2,
                            UpdatedAt = new DateTime(2025, 7, 20, 6, 46, 55, 206, DateTimeKind.Utc).AddTicks(6036)
                        });
                });

            modelBuilder.Entity("NafaPlace.Delivery.Domain.Models.CarrierZone", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("CarrierId")
                        .HasColumnType("integer");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<decimal>("DeliveryFee")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int>("EstimatedDeliveryDays")
                        .HasColumnType("integer");

                    b.Property<bool>("ExpressDeliveryAvailable")
                        .HasColumnType("boolean");

                    b.Property<decimal?>("ExpressDeliveryFee")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("FreeDeliveryThreshold")
                        .HasColumnType("decimal(18,2)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<int>("MaxDeliveryDays")
                        .HasColumnType("integer");

                    b.Property<decimal?>("MaxOrderValue")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("MaxVolume")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("MaxWeight")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("MinOrderValue")
                        .HasColumnType("numeric");

                    b.Property<int>("Priority")
                        .HasColumnType("integer");

                    b.Property<bool>("SameDayDeliveryAvailable")
                        .HasColumnType("boolean");

                    b.Property<decimal?>("SameDayDeliveryFee")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<int>("ZoneId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("ZoneId");

                    b.HasIndex("CarrierId", "ZoneId")
                        .IsUnique();

                    b.ToTable("CarrierZones");
                });

            modelBuilder.Entity("NafaPlace.Delivery.Domain.Models.DeliveryOrder", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime?>("ActualDeliveryDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<decimal?>("AdditionalFees")
                        .HasColumnType("numeric");

                    b.Property<int>("CarrierId")
                        .HasColumnType("integer");

                    b.Property<int?>("CarrierId1")
                        .HasColumnType("integer");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("Currency")
                        .IsRequired()
                        .HasMaxLength(3)
                        .HasColumnType("character varying(3)");

                    b.Property<string>("CustomerEmail")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("CustomerFeedback")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<string>("CustomerId")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("CustomerName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("CustomerPhone")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<int?>("CustomerRating")
                        .HasColumnType("integer");

                    b.Property<string>("DeliveryAddress")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<int>("DeliveryAttempts")
                        .HasColumnType("integer");

                    b.Property<string>("DeliveryCity")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("DeliveryCountry")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<decimal>("DeliveryFee")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("DeliveryInstructions")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<double?>("DeliveryLatitude")
                        .HasColumnType("double precision");

                    b.Property<double?>("DeliveryLongitude")
                        .HasColumnType("double precision");

                    b.Property<string>("DeliveryNotes")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("DeliveryPersonName")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("DeliveryPersonPhone")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<string>("DeliveryPhotoUrl")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("DeliveryPostalCode")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<string>("DeliveryRegion")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<int?>("DeliveryZoneId")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("EstimatedDeliveryDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<decimal?>("InsuranceFee")
                        .HasColumnType("numeric");

                    b.Property<DateTime?>("LastAttemptDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("MaxDeliveryAttempts")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("NextAttemptDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("OrderId")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<decimal>("OrderValue")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int>("PackageCount")
                        .HasColumnType("integer");

                    b.Property<string>("PackageDescription")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<TimeSpan?>("PreferredDeliveryTimeEnd")
                        .HasColumnType("interval");

                    b.Property<TimeSpan?>("PreferredDeliveryTimeStart")
                        .HasColumnType("interval");

                    b.Property<DateTime?>("RatingDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ReceivedByName")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("ReceivedByPhone")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<DateTime?>("ScheduledDeliveryDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("SignatureUrl")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("SpecialRequirements")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<decimal>("TotalFee")
                        .HasColumnType("numeric");

                    b.Property<string>("TrackingNumber")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<int>("Type")
                        .HasColumnType("integer");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<decimal?>("Volume")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("Weight")
                        .HasColumnType("numeric");

                    b.Property<int>("ZoneId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("CarrierId");

                    b.HasIndex("CarrierId1");

                    b.HasIndex("DeliveryZoneId");

                    b.HasIndex("ZoneId");

                    b.ToTable("DeliveryOrders");
                });

            modelBuilder.Entity("NafaPlace.Delivery.Domain.Models.DeliveryTracking", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("DeliveryOrderId")
                        .HasColumnType("integer");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("EventBy")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime>("EventDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsAutomated")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsCustomerVisible")
                        .HasColumnType("boolean");

                    b.Property<double?>("Latitude")
                        .HasColumnType("double precision");

                    b.Property<string>("Location")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<double?>("Longitude")
                        .HasColumnType("double precision");

                    b.Property<string>("Notes")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("PhotoUrl")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("DeliveryOrderId");

                    b.ToTable("DeliveryTrackings");
                });

            modelBuilder.Entity("NafaPlace.Delivery.Domain.Models.DeliveryZone", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<decimal>("BaseDeliveryFee")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("Boundaries")
                        .HasColumnType("text");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("Currency")
                        .IsRequired()
                        .HasMaxLength(3)
                        .HasColumnType("character varying(3)");

                    b.Property<string>("DeliveryDays")
                        .HasColumnType("text");

                    b.Property<TimeSpan?>("DeliveryEndTime")
                        .HasColumnType("interval");

                    b.Property<TimeSpan?>("DeliveryStartTime")
                        .HasColumnType("interval");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<int>("EstimatedDeliveryDays")
                        .HasColumnType("integer");

                    b.Property<bool>("ExpressDeliveryAvailable")
                        .HasColumnType("boolean");

                    b.Property<decimal?>("ExpressDeliveryFee")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("FreeDeliveryThreshold")
                        .HasColumnType("decimal(18,2)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<double?>("Latitude")
                        .HasColumnType("double precision");

                    b.Property<double?>("Longitude")
                        .HasColumnType("double precision");

                    b.Property<int>("MaxDeliveryDays")
                        .HasColumnType("integer");

                    b.Property<decimal?>("MaxOrderValue")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("MaxVolume")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("MaxWeight")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("MinOrderValue")
                        .HasColumnType("numeric");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("ParentZoneCode")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<double?>("Radius")
                        .HasColumnType("double precision");

                    b.Property<bool>("SameDayDeliveryAvailable")
                        .HasColumnType("boolean");

                    b.Property<decimal?>("SameDayDeliveryFee")
                        .HasColumnType("numeric");

                    b.Property<int>("Type")
                        .HasColumnType("integer");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.HasKey("Id");

                    b.ToTable("DeliveryZones");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            BaseDeliveryFee = 15000m,
                            Code = "CKY-CTR",
                            CreatedAt = new DateTime(2025, 7, 20, 6, 46, 55, 206, DateTimeKind.Utc).AddTicks(1158),
                            Currency = "GNF",
                            Description = "Centre-ville de Conakry",
                            EstimatedDeliveryDays = 1,
                            ExpressDeliveryAvailable = false,
                            FreeDeliveryThreshold = 500000m,
                            IsActive = true,
                            MaxDeliveryDays = 2,
                            Name = "Conakry Centre",
                            SameDayDeliveryAvailable = false,
                            Type = 0,
                            UpdatedAt = new DateTime(2025, 7, 20, 6, 46, 55, 206, DateTimeKind.Utc).AddTicks(1160)
                        },
                        new
                        {
                            Id = 2,
                            BaseDeliveryFee = 25000m,
                            Code = "CKY-PER",
                            CreatedAt = new DateTime(2025, 7, 20, 6, 46, 55, 206, DateTimeKind.Utc).AddTicks(2389),
                            Currency = "GNF",
                            Description = "Banlieue de Conakry",
                            EstimatedDeliveryDays = 2,
                            ExpressDeliveryAvailable = false,
                            FreeDeliveryThreshold = 500000m,
                            IsActive = true,
                            MaxDeliveryDays = 3,
                            Name = "Conakry Périphérie",
                            SameDayDeliveryAvailable = false,
                            Type = 0,
                            UpdatedAt = new DateTime(2025, 7, 20, 6, 46, 55, 206, DateTimeKind.Utc).AddTicks(2389)
                        },
                        new
                        {
                            Id = 3,
                            BaseDeliveryFee = 50000m,
                            Code = "KIN",
                            CreatedAt = new DateTime(2025, 7, 20, 6, 46, 55, 206, DateTimeKind.Utc).AddTicks(2392),
                            Currency = "GNF",
                            Description = "Région de Kindia",
                            EstimatedDeliveryDays = 3,
                            ExpressDeliveryAvailable = false,
                            FreeDeliveryThreshold = 750000m,
                            IsActive = true,
                            MaxDeliveryDays = 5,
                            Name = "Kindia",
                            SameDayDeliveryAvailable = false,
                            Type = 0,
                            UpdatedAt = new DateTime(2025, 7, 20, 6, 46, 55, 206, DateTimeKind.Utc).AddTicks(2393)
                        },
                        new
                        {
                            Id = 4,
                            BaseDeliveryFee = 60000m,
                            Code = "BOK",
                            CreatedAt = new DateTime(2025, 7, 20, 6, 46, 55, 206, DateTimeKind.Utc).AddTicks(2402),
                            Currency = "GNF",
                            Description = "Région de Boké",
                            EstimatedDeliveryDays = 4,
                            ExpressDeliveryAvailable = false,
                            FreeDeliveryThreshold = 750000m,
                            IsActive = true,
                            MaxDeliveryDays = 6,
                            Name = "Boké",
                            SameDayDeliveryAvailable = false,
                            Type = 0,
                            UpdatedAt = new DateTime(2025, 7, 20, 6, 46, 55, 206, DateTimeKind.Utc).AddTicks(2402)
                        },
                        new
                        {
                            Id = 5,
                            BaseDeliveryFee = 70000m,
                            Code = "LAB",
                            CreatedAt = new DateTime(2025, 7, 20, 6, 46, 55, 206, DateTimeKind.Utc).AddTicks(2404),
                            Currency = "GNF",
                            Description = "Région de Labé",
                            EstimatedDeliveryDays = 5,
                            ExpressDeliveryAvailable = false,
                            FreeDeliveryThreshold = 1000000m,
                            IsActive = true,
                            MaxDeliveryDays = 7,
                            Name = "Labé",
                            SameDayDeliveryAvailable = false,
                            Type = 0,
                            UpdatedAt = new DateTime(2025, 7, 20, 6, 46, 55, 206, DateTimeKind.Utc).AddTicks(2404)
                        });
                });

            modelBuilder.Entity("NafaPlace.Delivery.Domain.Models.CarrierZone", b =>
                {
                    b.HasOne("NafaPlace.Delivery.Domain.Models.Carrier", "Carrier")
                        .WithMany("CarrierZones")
                        .HasForeignKey("CarrierId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("NafaPlace.Delivery.Domain.Models.DeliveryZone", "Zone")
                        .WithMany("CarrierZones")
                        .HasForeignKey("ZoneId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Carrier");

                    b.Navigation("Zone");
                });

            modelBuilder.Entity("NafaPlace.Delivery.Domain.Models.DeliveryOrder", b =>
                {
                    b.HasOne("NafaPlace.Delivery.Domain.Models.Carrier", "Carrier")
                        .WithMany()
                        .HasForeignKey("CarrierId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("NafaPlace.Delivery.Domain.Models.Carrier", null)
                        .WithMany("DeliveryOrders")
                        .HasForeignKey("CarrierId1");

                    b.HasOne("NafaPlace.Delivery.Domain.Models.DeliveryZone", null)
                        .WithMany("DeliveryOrders")
                        .HasForeignKey("DeliveryZoneId");

                    b.HasOne("NafaPlace.Delivery.Domain.Models.DeliveryZone", "Zone")
                        .WithMany()
                        .HasForeignKey("ZoneId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Carrier");

                    b.Navigation("Zone");
                });

            modelBuilder.Entity("NafaPlace.Delivery.Domain.Models.DeliveryTracking", b =>
                {
                    b.HasOne("NafaPlace.Delivery.Domain.Models.DeliveryOrder", "DeliveryOrder")
                        .WithMany("TrackingEvents")
                        .HasForeignKey("DeliveryOrderId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("DeliveryOrder");
                });

            modelBuilder.Entity("NafaPlace.Delivery.Domain.Models.Carrier", b =>
                {
                    b.Navigation("CarrierZones");

                    b.Navigation("DeliveryOrders");
                });

            modelBuilder.Entity("NafaPlace.Delivery.Domain.Models.DeliveryOrder", b =>
                {
                    b.Navigation("TrackingEvents");
                });

            modelBuilder.Entity("NafaPlace.Delivery.Domain.Models.DeliveryZone", b =>
                {
                    b.Navigation("CarrierZones");

                    b.Navigation("DeliveryOrders");
                });
#pragma warning restore 612, 618
        }
    }
}
