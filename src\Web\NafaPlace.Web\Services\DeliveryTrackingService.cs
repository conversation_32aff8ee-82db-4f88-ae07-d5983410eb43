using System.Text.Json;

namespace NafaPlace.Web.Services;

public interface IDeliveryTrackingService
{
    Task<List<DeliveryTrackingDto>> GetDeliveryTrackingAsync(string trackingNumber);
    Task<decimal> CalculateDeliveryFeeAsync(string address, decimal orderAmount, decimal? weight = null);
    Task<List<DeliveryQuoteDto>> GetDeliveryQuotesAsync(string address, decimal orderAmount, decimal? weight = null, decimal? volume = null);
}

public class DeliveryTrackingService : IDeliveryTrackingService
{
    private readonly HttpClient _httpClient;
    private readonly ILogger<DeliveryTrackingService> _logger;
    private readonly JsonSerializerOptions _jsonOptions;

    public DeliveryTrackingService(HttpClient httpClient, ILogger<DeliveryTrackingService> logger)
    {
        _httpClient = httpClient;
        _logger = logger;
        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            PropertyNameCaseInsensitive = true
        };
    }

    public async Task<List<DeliveryTrackingDto>> GetDeliveryTrackingAsync(string trackingNumber)
    {
        try
        {
            var response = await _httpClient.GetAsync($"api/delivery/orders/tracking/{trackingNumber}");
            
            if (response.IsSuccessStatusCode)
            {
                var json = await response.Content.ReadAsStringAsync();
                return JsonSerializer.Deserialize<List<DeliveryTrackingDto>>(json, _jsonOptions) ?? new List<DeliveryTrackingDto>();
            }
            
            _logger.LogWarning("Failed to get delivery tracking for {TrackingNumber}", trackingNumber);
            return new List<DeliveryTrackingDto>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting delivery tracking for {TrackingNumber}", trackingNumber);
            return new List<DeliveryTrackingDto>();
        }
    }

    public async Task<decimal> CalculateDeliveryFeeAsync(string address, decimal orderAmount, decimal? weight = null)
    {
        try
        {
            var response = await _httpClient.GetAsync($"api/orders/delivery/calculate-fee?address={Uri.EscapeDataString(address)}&orderAmount={orderAmount}&weight={weight}");
            
            if (response.IsSuccessStatusCode)
            {
                var feeString = await response.Content.ReadAsStringAsync();
                if (decimal.TryParse(feeString, out var fee))
                {
                    return fee;
                }
            }
            
            _logger.LogWarning("Failed to calculate delivery fee, using default");
            return 25000; // Default fee
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calculating delivery fee");
            return 25000; // Default fee
        }
    }

    public async Task<List<DeliveryQuoteDto>> GetDeliveryQuotesAsync(string address, decimal orderAmount, decimal? weight = null, decimal? volume = null)
    {
        try
        {
            var request = new
            {
                Address = address,
                OrderAmount = orderAmount,
                Weight = weight,
                Volume = volume
            };

            var response = await _httpClient.PostAsJsonAsync("api/orders/delivery/quotes", request);
            
            if (response.IsSuccessStatusCode)
            {
                var json = await response.Content.ReadAsStringAsync();
                return JsonSerializer.Deserialize<List<DeliveryQuoteDto>>(json, _jsonOptions) ?? new List<DeliveryQuoteDto>();
            }
            
            _logger.LogWarning("Failed to get delivery quotes");
            return new List<DeliveryQuoteDto>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting delivery quotes");
            return new List<DeliveryQuoteDto>();
        }
    }
}

public class DeliveryTrackingDto
{
    public int Id { get; set; }
    public int DeliveryOrderId { get; set; }
    public string Status { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string? Location { get; set; }
    public double? Latitude { get; set; }
    public double? Longitude { get; set; }
    public DateTime EventDate { get; set; }
    public string? EventBy { get; set; }
    public string? Notes { get; set; }
    public string? PhotoUrl { get; set; }
    public bool IsCustomerVisible { get; set; }
    public bool IsAutomated { get; set; }
}

public class DeliveryQuoteDto
{
    public int CarrierId { get; set; }
    public string CarrierName { get; set; } = string.Empty;
    public int ZoneId { get; set; }
    public string ZoneName { get; set; } = string.Empty;
    public decimal DeliveryFee { get; set; }
    public decimal TotalFee { get; set; }
    public string Currency { get; set; } = "GNF";
    public int EstimatedDeliveryDays { get; set; }
    public int MaxDeliveryDays { get; set; }
    public DateTime EstimatedDeliveryDate { get; set; }
    public bool IsAvailable { get; set; }
    public bool SameDayAvailable { get; set; }
    public decimal? SameDayFee { get; set; }
    public bool ExpressAvailable { get; set; }
    public decimal? ExpressFee { get; set; }
    public decimal? InsuranceFee { get; set; }
}

public enum DeliveryStatus
{
    Pending = 1,
    Confirmed = 2,
    PickedUp = 3,
    InTransit = 4,
    OutForDelivery = 5,
    Delivered = 6,
    Failed = 7,
    Returned = 8,
    Cancelled = 9,
    Lost = 10,
    Damaged = 11
}
