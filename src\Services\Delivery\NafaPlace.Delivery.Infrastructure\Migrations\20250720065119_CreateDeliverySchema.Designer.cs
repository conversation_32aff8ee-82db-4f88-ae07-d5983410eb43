﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using NafaPlace.Delivery.Infrastructure.Data;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace NafaPlace.Delivery.Infrastructure.Migrations
{
    [DbContext(typeof(DeliveryDbContext))]
    [Migration("20250720065119_CreateDeliverySchema")]
    partial class CreateDeliverySchema
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.0")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("NafaPlace.Delivery.Domain.Models.Carrier", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Address")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("ApiEndpoint")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("ApiKey")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("ApiSecret")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("ContactEmail")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("ContactPhone")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<string>("LogoUrl")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<decimal>("Rating")
                        .HasColumnType("numeric");

                    b.Property<int>("ReviewCount")
                        .HasColumnType("integer");

                    b.Property<int>("SuccessfulDeliveries")
                        .HasColumnType("integer");

                    b.Property<int>("TotalDeliveries")
                        .HasColumnType("integer");

                    b.Property<int>("Type")
                        .HasColumnType("integer");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("Website")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.HasKey("Id");

                    b.ToTable("Carriers");
                });

            modelBuilder.Entity("NafaPlace.Delivery.Domain.Models.CarrierZone", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("CarrierId")
                        .HasColumnType("integer");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<decimal>("DeliveryFee")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int>("EstimatedDeliveryDays")
                        .HasColumnType("integer");

                    b.Property<bool>("ExpressDeliveryAvailable")
                        .HasColumnType("boolean");

                    b.Property<decimal?>("ExpressDeliveryFee")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("FreeDeliveryThreshold")
                        .HasColumnType("decimal(18,2)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<int>("MaxDeliveryDays")
                        .HasColumnType("integer");

                    b.Property<decimal?>("MaxOrderValue")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("MaxVolume")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("MaxWeight")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("MinOrderValue")
                        .HasColumnType("numeric");

                    b.Property<int>("Priority")
                        .HasColumnType("integer");

                    b.Property<bool>("SameDayDeliveryAvailable")
                        .HasColumnType("boolean");

                    b.Property<decimal?>("SameDayDeliveryFee")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<int>("ZoneId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("ZoneId");

                    b.HasIndex("CarrierId", "ZoneId")
                        .IsUnique();

                    b.ToTable("CarrierZones");
                });

            modelBuilder.Entity("NafaPlace.Delivery.Domain.Models.DeliveryOrder", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime?>("ActualDeliveryDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<decimal?>("AdditionalFees")
                        .HasColumnType("numeric");

                    b.Property<int>("CarrierId")
                        .HasColumnType("integer");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("Currency")
                        .IsRequired()
                        .HasMaxLength(3)
                        .HasColumnType("character varying(3)");

                    b.Property<string>("CustomerEmail")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("CustomerFeedback")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<string>("CustomerId")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("CustomerName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("CustomerPhone")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<int?>("CustomerRating")
                        .HasColumnType("integer");

                    b.Property<string>("DeliveryAddress")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<int>("DeliveryAttempts")
                        .HasColumnType("integer");

                    b.Property<string>("DeliveryCity")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("DeliveryCountry")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<decimal>("DeliveryFee")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("DeliveryInstructions")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<double?>("DeliveryLatitude")
                        .HasColumnType("double precision");

                    b.Property<double?>("DeliveryLongitude")
                        .HasColumnType("double precision");

                    b.Property<string>("DeliveryNotes")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("DeliveryPersonName")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("DeliveryPersonPhone")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<string>("DeliveryPhotoUrl")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("DeliveryPostalCode")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<string>("DeliveryRegion")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime?>("EstimatedDeliveryDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<decimal?>("InsuranceFee")
                        .HasColumnType("numeric");

                    b.Property<DateTime?>("LastAttemptDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("MaxDeliveryAttempts")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("NextAttemptDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("OrderId")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<decimal>("OrderValue")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int>("PackageCount")
                        .HasColumnType("integer");

                    b.Property<string>("PackageDescription")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<TimeSpan?>("PreferredDeliveryTimeEnd")
                        .HasColumnType("interval");

                    b.Property<TimeSpan?>("PreferredDeliveryTimeStart")
                        .HasColumnType("interval");

                    b.Property<DateTime?>("RatingDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ReceivedByName")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("ReceivedByPhone")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<DateTime?>("ScheduledDeliveryDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("SignatureUrl")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("SpecialRequirements")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<decimal>("TotalFee")
                        .HasColumnType("numeric");

                    b.Property<string>("TrackingNumber")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<int>("Type")
                        .HasColumnType("integer");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<decimal?>("Volume")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("Weight")
                        .HasColumnType("numeric");

                    b.Property<int>("ZoneId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("CarrierId");

                    b.HasIndex("ZoneId");

                    b.ToTable("DeliveryOrders");
                });

            modelBuilder.Entity("NafaPlace.Delivery.Domain.Models.DeliveryRoute", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<decimal?>("ActualDuration")
                        .HasColumnType("numeric");

                    b.Property<int>("CarrierId")
                        .HasColumnType("integer");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<int>("CompletedDeliveries")
                        .HasColumnType("integer");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("DriverId")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("DriverName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("DriverPhone")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<DateTime?>("EndTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<decimal?>("EstimatedDuration")
                        .HasColumnType("numeric");

                    b.Property<int>("FailedDeliveries")
                        .HasColumnType("integer");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Notes")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<DateTime>("RouteDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("StartTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<int>("TotalDeliveries")
                        .HasColumnType("integer");

                    b.Property<decimal?>("TotalDistance")
                        .HasColumnType("numeric");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("VehicleId")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("VehiclePlate")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.HasKey("Id");

                    b.HasIndex("CarrierId");

                    b.ToTable("DeliveryRoute");
                });

            modelBuilder.Entity("NafaPlace.Delivery.Domain.Models.DeliveryTracking", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("DeliveryOrderId")
                        .HasColumnType("integer");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("EventBy")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime>("EventDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsAutomated")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsCustomerVisible")
                        .HasColumnType("boolean");

                    b.Property<double?>("Latitude")
                        .HasColumnType("double precision");

                    b.Property<string>("Location")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<double?>("Longitude")
                        .HasColumnType("double precision");

                    b.Property<string>("Notes")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("PhotoUrl")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("DeliveryOrderId");

                    b.ToTable("DeliveryTrackings");
                });

            modelBuilder.Entity("NafaPlace.Delivery.Domain.Models.DeliveryZone", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<decimal>("BaseDeliveryFee")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("Boundaries")
                        .HasColumnType("text");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("Currency")
                        .IsRequired()
                        .HasMaxLength(3)
                        .HasColumnType("character varying(3)");

                    b.Property<string>("DeliveryDays")
                        .HasColumnType("text");

                    b.Property<TimeSpan?>("DeliveryEndTime")
                        .HasColumnType("interval");

                    b.Property<TimeSpan?>("DeliveryStartTime")
                        .HasColumnType("interval");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<int>("EstimatedDeliveryDays")
                        .HasColumnType("integer");

                    b.Property<bool>("ExpressDeliveryAvailable")
                        .HasColumnType("boolean");

                    b.Property<decimal?>("ExpressDeliveryFee")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("FreeDeliveryThreshold")
                        .HasColumnType("decimal(18,2)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<double?>("Latitude")
                        .HasColumnType("double precision");

                    b.Property<double?>("Longitude")
                        .HasColumnType("double precision");

                    b.Property<int>("MaxDeliveryDays")
                        .HasColumnType("integer");

                    b.Property<decimal?>("MaxOrderValue")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("MaxVolume")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("MaxWeight")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("MinOrderValue")
                        .HasColumnType("numeric");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("ParentZoneCode")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<double?>("Radius")
                        .HasColumnType("double precision");

                    b.Property<bool>("SameDayDeliveryAvailable")
                        .HasColumnType("boolean");

                    b.Property<decimal?>("SameDayDeliveryFee")
                        .HasColumnType("numeric");

                    b.Property<int>("Type")
                        .HasColumnType("integer");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.HasKey("Id");

                    b.ToTable("DeliveryZones");
                });

            modelBuilder.Entity("NafaPlace.Delivery.Domain.Models.RouteDelivery", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime?>("ActualArrival")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("ActualDeparture")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("DeliveryOrderId")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("EstimatedArrival")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("EstimatedDeparture")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Notes")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<int>("RouteId")
                        .HasColumnType("integer");

                    b.Property<int>("Sequence")
                        .HasColumnType("integer");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("DeliveryOrderId");

                    b.HasIndex("RouteId");

                    b.ToTable("RouteDelivery");
                });

            modelBuilder.Entity("NafaPlace.Delivery.Domain.Models.CarrierZone", b =>
                {
                    b.HasOne("NafaPlace.Delivery.Domain.Models.Carrier", "Carrier")
                        .WithMany("CarrierZones")
                        .HasForeignKey("CarrierId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("NafaPlace.Delivery.Domain.Models.DeliveryZone", "Zone")
                        .WithMany("CarrierZones")
                        .HasForeignKey("ZoneId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Carrier");

                    b.Navigation("Zone");
                });

            modelBuilder.Entity("NafaPlace.Delivery.Domain.Models.DeliveryOrder", b =>
                {
                    b.HasOne("NafaPlace.Delivery.Domain.Models.Carrier", "Carrier")
                        .WithMany("DeliveryOrders")
                        .HasForeignKey("CarrierId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("NafaPlace.Delivery.Domain.Models.DeliveryZone", "Zone")
                        .WithMany("DeliveryOrders")
                        .HasForeignKey("ZoneId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Carrier");

                    b.Navigation("Zone");
                });

            modelBuilder.Entity("NafaPlace.Delivery.Domain.Models.DeliveryRoute", b =>
                {
                    b.HasOne("NafaPlace.Delivery.Domain.Models.Carrier", "Carrier")
                        .WithMany()
                        .HasForeignKey("CarrierId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Carrier");
                });

            modelBuilder.Entity("NafaPlace.Delivery.Domain.Models.DeliveryTracking", b =>
                {
                    b.HasOne("NafaPlace.Delivery.Domain.Models.DeliveryOrder", "DeliveryOrder")
                        .WithMany("TrackingEvents")
                        .HasForeignKey("DeliveryOrderId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("DeliveryOrder");
                });

            modelBuilder.Entity("NafaPlace.Delivery.Domain.Models.RouteDelivery", b =>
                {
                    b.HasOne("NafaPlace.Delivery.Domain.Models.DeliveryOrder", "DeliveryOrder")
                        .WithMany()
                        .HasForeignKey("DeliveryOrderId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("NafaPlace.Delivery.Domain.Models.DeliveryRoute", "Route")
                        .WithMany("RouteDeliveries")
                        .HasForeignKey("RouteId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("DeliveryOrder");

                    b.Navigation("Route");
                });

            modelBuilder.Entity("NafaPlace.Delivery.Domain.Models.Carrier", b =>
                {
                    b.Navigation("CarrierZones");

                    b.Navigation("DeliveryOrders");
                });

            modelBuilder.Entity("NafaPlace.Delivery.Domain.Models.DeliveryOrder", b =>
                {
                    b.Navigation("TrackingEvents");
                });

            modelBuilder.Entity("NafaPlace.Delivery.Domain.Models.DeliveryRoute", b =>
                {
                    b.Navigation("RouteDeliveries");
                });

            modelBuilder.Entity("NafaPlace.Delivery.Domain.Models.DeliveryZone", b =>
                {
                    b.Navigation("CarrierZones");

                    b.Navigation("DeliveryOrders");
                });
#pragma warning restore 612, 618
        }
    }
}
