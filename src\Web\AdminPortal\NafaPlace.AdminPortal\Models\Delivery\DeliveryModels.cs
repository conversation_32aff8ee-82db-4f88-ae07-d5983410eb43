using System.ComponentModel.DataAnnotations;

namespace NafaPlace.AdminPortal.Models.Delivery;

public class DeliveryZoneDto
{
    public int Id { get; set; }
    
    [Required(ErrorMessage = "Le nom est requis")]
    [StringLength(100, ErrorMessage = "Le nom ne peut pas dépasser 100 caractères")]
    public string Name { get; set; } = string.Empty;
    
    [Required(ErrorMessage = "Le code est requis")]
    [StringLength(50, ErrorMessage = "Le code ne peut pas dépasser 50 caractères")]
    public string Code { get; set; } = string.Empty;
    
    [StringLength(500, ErrorMessage = "La description ne peut pas dépasser 500 caractères")]
    public string? Description { get; set; }
    
    public ZoneType Type { get; set; }
    public string? ParentZoneCode { get; set; }
    public double? Latitude { get; set; }
    public double? Longitude { get; set; }
    public double? Radius { get; set; }
    
    [Required(ErrorMessage = "Les frais de livraison de base sont requis")]
    [Range(0, double.MaxValue, ErrorMessage = "Les frais de livraison doivent être positifs")]
    public decimal BaseDeliveryFee { get; set; }
    
    [Range(0, double.MaxValue, ErrorMessage = "Le seuil de livraison gratuite doit être positif")]
    public decimal? FreeDeliveryThreshold { get; set; }
    
    [Range(1, int.MaxValue, ErrorMessage = "Les jours de livraison estimés doivent être au moins 1")]
    public int EstimatedDeliveryDays { get; set; } = 1;
    
    [Range(1, int.MaxValue, ErrorMessage = "Les jours de livraison maximum doivent être au moins 1")]
    public int MaxDeliveryDays { get; set; } = 7;
    
    public bool SameDayDeliveryAvailable { get; set; }
    
    [Range(0, double.MaxValue, ErrorMessage = "Les frais de livraison le jour même doivent être positifs")]
    public decimal? SameDayDeliveryFee { get; set; }
    
    public bool ExpressDeliveryAvailable { get; set; }
    
    [Range(0, double.MaxValue, ErrorMessage = "Les frais de livraison express doivent être positifs")]
    public decimal? ExpressDeliveryFee { get; set; }
    
    public string Currency { get; set; } = "GNF";
    public bool IsActive { get; set; } = true;
}

public class CarrierDto
{
    public int Id { get; set; }
    
    [Required(ErrorMessage = "Le nom est requis")]
    [StringLength(100, ErrorMessage = "Le nom ne peut pas dépasser 100 caractères")]
    public string Name { get; set; } = string.Empty;
    
    [Required(ErrorMessage = "Le code est requis")]
    [StringLength(50, ErrorMessage = "Le code ne peut pas dépasser 50 caractères")]
    public string Code { get; set; } = string.Empty;
    
    [StringLength(500, ErrorMessage = "La description ne peut pas dépasser 500 caractères")]
    public string? Description { get; set; }
    
    public string? LogoUrl { get; set; }
    
    [EmailAddress(ErrorMessage = "Format d'email invalide")]
    public string? ContactEmail { get; set; }
    
    [Phone(ErrorMessage = "Format de téléphone invalide")]
    public string? ContactPhone { get; set; }
    
    public string? Address { get; set; }
    
    [Url(ErrorMessage = "Format d'URL invalide")]
    public string? Website { get; set; }
    
    public CarrierType Type { get; set; }
    public decimal Rating { get; set; }
    public int ReviewCount { get; set; }
    public int TotalDeliveries { get; set; }
    public int SuccessfulDeliveries { get; set; }
    public bool IsActive { get; set; } = true;
    
    public decimal SuccessRate => TotalDeliveries > 0 ? (decimal)SuccessfulDeliveries / TotalDeliveries * 100 : 0;
}

public class CarrierZoneDto
{
    public int Id { get; set; }
    public int CarrierId { get; set; }
    public string CarrierName { get; set; } = string.Empty;
    public int ZoneId { get; set; }
    public string ZoneName { get; set; } = string.Empty;
    
    [Required(ErrorMessage = "Les frais de livraison sont requis")]
    [Range(0, double.MaxValue, ErrorMessage = "Les frais de livraison doivent être positifs")]
    public decimal DeliveryFee { get; set; }
    
    [Range(0, double.MaxValue, ErrorMessage = "Le seuil de livraison gratuite doit être positif")]
    public decimal? FreeDeliveryThreshold { get; set; }
    
    [Range(1, int.MaxValue, ErrorMessage = "Les jours de livraison estimés doivent être au moins 1")]
    public int EstimatedDeliveryDays { get; set; } = 1;
    
    [Range(1, int.MaxValue, ErrorMessage = "Les jours de livraison maximum doivent être au moins 1")]
    public int MaxDeliveryDays { get; set; } = 7;
    
    public bool SameDayDeliveryAvailable { get; set; }
    
    [Range(0, double.MaxValue, ErrorMessage = "Les frais de livraison le jour même doivent être positifs")]
    public decimal? SameDayDeliveryFee { get; set; }
    
    public bool ExpressDeliveryAvailable { get; set; }
    
    [Range(0, double.MaxValue, ErrorMessage = "Les frais de livraison express doivent être positifs")]
    public decimal? ExpressDeliveryFee { get; set; }
    
    public bool IsActive { get; set; } = true;
}

public class DeliveryOrderDto
{
    public int Id { get; set; }
    public string OrderId { get; set; } = string.Empty;
    public string TrackingNumber { get; set; } = string.Empty;
    public string CustomerId { get; set; } = string.Empty;
    public string CustomerName { get; set; } = string.Empty;
    public string CustomerEmail { get; set; } = string.Empty;
    public string CustomerPhone { get; set; } = string.Empty;
    public string DeliveryAddress { get; set; } = string.Empty;
    public string? DeliveryCity { get; set; }
    public string? DeliveryRegion { get; set; }
    public int ZoneId { get; set; }
    public string ZoneName { get; set; } = string.Empty;
    public int CarrierId { get; set; }
    public string CarrierName { get; set; } = string.Empty;
    public decimal DeliveryFee { get; set; }
    public decimal TotalFee { get; set; }
    public string Currency { get; set; } = "GNF";
    public decimal OrderValue { get; set; }
    public decimal? Weight { get; set; }
    public int PackageCount { get; set; }
    public DeliveryType Type { get; set; }
    public DeliveryStatus Status { get; set; }
    public DateTime? ScheduledDeliveryDate { get; set; }
    public DateTime? EstimatedDeliveryDate { get; set; }
    public DateTime? ActualDeliveryDate { get; set; }
    public string? DeliveryInstructions { get; set; }
    public int DeliveryAttempts { get; set; }
    public int? CustomerRating { get; set; }
    public string? CustomerFeedback { get; set; }
    public DateTime CreatedAt { get; set; }
    public List<DeliveryTrackingDto> TrackingEvents { get; set; } = new();
}

public class DeliveryTrackingDto
{
    public int Id { get; set; }
    public int DeliveryOrderId { get; set; }
    public DeliveryStatus Status { get; set; }
    public string Description { get; set; } = string.Empty;
    public string? Location { get; set; }
    public double? Latitude { get; set; }
    public double? Longitude { get; set; }
    public DateTime EventDate { get; set; }
    public string? EventBy { get; set; }
    public string? Notes { get; set; }
    public string? PhotoUrl { get; set; }
    public bool IsCustomerVisible { get; set; }
    public bool IsAutomated { get; set; }
}

public enum ZoneType
{
    Country = 1,
    Region = 2,
    Prefecture = 3,
    City = 4,
    District = 5,
    Custom = 6
}

public enum CarrierType
{
    Internal = 1,
    ThirdParty = 2,
    Partner = 3
}

public enum DeliveryType
{
    Standard = 1,
    Express = 2,
    SameDay = 3,
    Scheduled = 4
}

public enum DeliveryStatus
{
    Pending = 1,
    Confirmed = 2,
    PickedUp = 3,
    InTransit = 4,
    OutForDelivery = 5,
    Delivered = 6,
    Failed = 7,
    Returned = 8,
    Cancelled = 9,
    Lost = 10,
    Damaged = 11
}
